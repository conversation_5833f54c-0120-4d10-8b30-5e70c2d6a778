import pygame
import json

class ShopCard:
    def __init__(self, item_data, x, y, width, height, item_type="car", is_owned=False):
        self.item_data = item_data
        self.rect = pygame.Rect(x, y, width, height)
        self.item_type = item_type
        self.is_hovered = False
        self.is_owned = is_owned
        self.font = pygame.font.SysFont("arial", 24)
        self.header_font = pygame.font.SysFont("arial", 32)
        
        # Load item image if it's a car
        self.item_image = None
        if item_type == "car":
            self.load_car_image()
    
    def load_car_image(self):
        try:
            color_name = self.item_data["color"]["0"]  # Default to first color
            image_path = f"assets/img/{self.item_data['name']}_{color_name}_car.png"
            self.item_image = pygame.image.load(image_path)
            self.item_image = pygame.transform.scale(self.item_image, (150, 100))
        except:
            self.item_image = pygame.Surface((150, 100))
            self.item_image.fill((100, 100, 100))
    
    def update(self, mouse_pos, mouse_click):
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        if self.is_hovered and mouse_click[0]:
            return True
        return False
    
    def draw(self, screen):
        # Draw card background
        if self.is_owned:
            bg_color = (20, 80, 20) if self.is_hovered else (10, 60, 10)
            border_color = (0, 255, 0)
        else:
            bg_color = (60, 60, 60) if self.is_hovered else (40, 40, 40)
            border_color = (200, 200, 200)
        
        pygame.draw.rect(screen, bg_color, self.rect)
        pygame.draw.rect(screen, border_color, self.rect, 1)
        
        # Draw item image or icon
        if self.item_image:
            image_x = self.rect.x + (self.rect.width - self.item_image.get_width()) // 2
            image_y = self.rect.y + 10
            screen.blit(self.item_image, (image_x, image_y))
        
        # Draw item info
        y_offset = self.rect.y + (120 if self.item_type == "car" else 20)
        
        # Item name
        name = self.item_data.get("name", "Unknown")
        name_color = (150, 255, 150) if self.is_owned else (255, 255, 255)
        name_text = self.header_font.render(name.title(), True, name_color)
        screen.blit(name_text, (self.rect.x + 10, y_offset))
        y_offset += 35
        
        # Show "OWNED" label if owned
        if self.is_owned:
            owned_text = self.font.render("POSIADANE", True, (0, 255, 0))
            screen.blit(owned_text, (self.rect.x + 10, y_offset))
            y_offset += 25
        
        # Draw stats based on item type
        if self.item_type == "car":
            # Find engine part in parts dict
            engine_part = self.item_data['parts'].get('engine', None)
            horsepower = engine_part['horsepower'] if engine_part else 'N/A'
            stats = [
                f"Waga: {self.item_data['weight']} kg",
                f"Wartość: {self.item_data['value']} $",
                f"Moc: {horsepower} KM"
            ]
        else:
            stats = []
            if "horsepower" in self.item_data:
                stats.append(f"Moc: {self.item_data['horsepower']} KM")
            if "horsepower_boost_percentage" in self.item_data:
                stats.append(f"Boost: +{self.item_data['horsepower_boost_percentage']}%")
            if "weight" in self.item_data:
                stats.append(f"Waga: {self.item_data['weight']} kg")
            stats.append(f"Cena: {self.item_data['value']} $")
        
        for stat in stats:
            stat_text = self.font.render(stat, True, (200, 200, 200))
            screen.blit(stat_text, (self.rect.x + 10, y_offset))
            y_offset += 25

class TabButton:
    def __init__(self, label, x, y, width, height):
        self.rect = pygame.Rect(x, y, width, height)
        self.label = label
        self.is_active = False
        self.is_hovered = False
        self.font = pygame.font.SysFont("arial", 32)
    
    def update(self, mouse_pos, mouse_click):
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        if self.is_hovered and mouse_click[0]:
            return True
        return False
    
    def draw(self, screen):
        # Draw background
        if self.is_active:
            bg_color = (0, 255, 255)
            text_color = (0, 0, 0)
        elif self.is_hovered:
            bg_color = (80, 80, 80)
            text_color = (255, 255, 255)
        else:
            bg_color = (50, 50, 50)
            text_color = (200, 200, 200)
        
        pygame.draw.rect(screen, bg_color, self.rect)
        pygame.draw.rect(screen, (200, 200, 200), self.rect, 1)
        
        # Draw label
        text = self.font.render(self.label, True, text_color)
        text_x = self.rect.x + (self.rect.width - text.get_width()) // 2
        text_y = self.rect.y + (self.rect.height - text.get_height()) // 2
        screen.blit(text, (text_x, text_y))

class ConfirmDialog:
    def __init__(self, item_name, price, s_width, s_height):
        self.width = 400
        self.height = 200
        self.rect = pygame.Rect(
            (s_width - self.width) // 2,
            (s_height - self.height) // 2,
            self.width,
            self.height
        )
        self.item_name = item_name
        self.price = price
        self.font = pygame.font.SysFont("arial", 24)
        
        # Create buttons
        button_y = self.rect.y + self.height - 60
        self.confirm_button = pygame.Rect(self.rect.x + 50, button_y, 120, 40)
        self.cancel_button = pygame.Rect(self.rect.x + self.width - 170, button_y, 120, 40)
    
    def update(self, mouse_pos, mouse_click):
        if mouse_click[0]:
            if self.confirm_button.collidepoint(mouse_pos):
                return "confirm"
            elif self.cancel_button.collidepoint(mouse_pos):
                return "cancel"
        return None
    
    def draw(self, screen):
        # Draw dialog background
        pygame.draw.rect(screen, (40, 40, 40), self.rect)
        pygame.draw.rect(screen, (200, 200, 200), self.rect, 2)
        
        # Draw text
        title = self.font.render(f"Kup {self.item_name}?", True, (255, 255, 255))
        price = self.font.render(f"Cena: {self.price} $", True, (200, 200, 200))
        
        screen.blit(title, (self.rect.x + (self.width - title.get_width()) // 2, self.rect.y + 30))
        screen.blit(price, (self.rect.x + (self.width - price.get_width()) // 2, self.rect.y + 70))
        
        # Draw buttons
        for button, text in [(self.confirm_button, "Kup"), (self.cancel_button, "Anuluj")]:
            pygame.draw.rect(screen, (60, 60, 60), button)
            pygame.draw.rect(screen, (200, 200, 200), button, 1)
            
            button_text = self.font.render(text, True, (255, 255, 255))
            text_x = button.x + (button.width - button_text.get_width()) // 2
            text_y = button.y + (button.height - button_text.get_height()) // 2
            screen.blit(button_text, (text_x, text_y))

def save_purchase(item_data, item_type):
    """Save the purchase to profile.json and update garage.json"""
    with open('data/profile.json', 'r') as f:
        profile = json.load(f)
    
    # Check if user has enough money
    if profile['money'] < item_data['value']:
        raise ValueError("Not enough money to buy this item.")
    
    # Deduct money
    profile['money'] -= item_data['value']
    
    # Add item to inventory
    if item_type == "car":
        if item_data["name"] not in profile["inventory"]["owned_cars"]:
            profile["inventory"]["owned_cars"].append(item_data["name"])
            # Update selected_car to the index of the newly bought car
            # We assume the car index is the length of owned_cars - 1
            profile["cars"]["selected_car"] = len(profile["inventory"]["owned_cars"]) - 1
            # Set default color to "1" if not set
            if "color" not in profile["cars"]:
                profile["cars"]["color"] = "1"
            
            # Update garage.json to include the new car
            try:
                with open('data/garage.json', 'r') as gf:
                    garage_data = json.load(gf)
            except FileNotFoundError:
                garage_data = []
            
            # Check if car already in garage_data
            car_names_in_garage = [car['name'] for car in garage_data]
            if item_data["name"] not in car_names_in_garage:
                # Load shop_data to get full car data
                with open('data/shop_data.json', 'r') as sf:
                    shop_data = json.load(sf)
                cars_list = shop_data[1].get('cars', [])
                # Find the car data by name
                car_to_add = next((car for car in cars_list if car['name'] == item_data["name"]), None)
                if car_to_add:
                    garage_data.append(car_to_add)
                    with open('data/garage.json', 'w') as gf:
                        json.dump(garage_data, gf, indent=4)
    else:  # part
        category = item_data["category"]  # category was added when flattening parts data
        if item_data["name"] not in profile["inventory"]["owned_parts"][category]:
            profile["inventory"]["owned_parts"][category].append(item_data["name"])
    
    with open('data/profile.json', 'w') as f:
        json.dump(profile, f, indent=4)
