import pygame
import time
import json
from cars import Car, OpponentCar
from background import Background
from map import Map
from ui import TimeToStart, draw_end_screen, draw_profile_screen, draw_garage_screen, shop_screen_draw, garage_screen_draw
from ui_components import TextButton

class Game:
    def __init__(self):
        pygame.init()
        self.screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
        self.s_width, self.s_height = self.screen.get_size()
        pygame.display.set_caption('Xtreme Cars')
        self.clock = pygame.time.Clock()
        self.running = True

    def run(self):
        self.main_menu()

    def main_menu(self):
        buttons = [
        TextButton('Single Player', self.s_width // 2 - 100, self.s_height // 2 - 175, action=self.single_player),
        TextButton('Multiplayer', self.s_width // 2 - 100, self.s_height // 2 - 100),
        TextButton('Profil', self.s_width // 2 - 100, self.s_height // 2 - 25, action=lambda: draw_profile_screen(self.s_width, self.s_height, self.screen)),
        TextButton('<PERSON><PERSON><PERSON>', self.s_width // 2 - 100, self.s_height // 2 + 50, action=lambda: garage_screen_draw(self.s_width, self.s_height, self.screen)),
        TextButton('Sklep', self.s_width // 2 - 100, self.s_height // 2 + 125, action=lambda: shop_screen_draw(self.s_width, self.s_height, self.screen)),
        TextButton('Ustawienia', self.s_width // 2 - 100, self.s_height // 2 + 200),
        TextButton('Wyjdź z gry', self.s_width // 2 - 100, self.s_height // 2 + 275, action=pygame.quit)
    ]
        background = Background('background', self.s_width, self.s_height)
        while self.running:
            mouse_pos = pygame.mouse.get_pos()
            mouse_click = pygame.mouse.get_pressed()
            background.draw(self.screen)
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False

            for button in buttons:
                button.update(mouse_pos, mouse_click)
                button.draw(self.screen)

            pygame.display.update()

    def single_player(self):
        bg = Map('map1', self.s_width, self.s_height)
        map_distance = bg.width * 2.5 - self.s_width
        distance_covered = 0
        is_map_ended = False

        with open('data/garage.json') as f:
            cars_data = json.load(f)
        with open('data/profile.json') as f:
            user_data = json.load(f)
        with open('data/oponent_levels.json') as f:
            oponent_data = json.load(f)

        selected_car_data = cars_data[user_data["cars"]["selected_car"]]
        opponent_data = oponent_data[user_data["level"]['current'] - 1]

        start_time = time.time()
        player = Car(
            selected_car_data["name"],
            selected_car_data["color"][user_data["cars"]["color"]],
            selected_car_data["weight"],
            selected_car_data["parts"],
            100, self.s_height // 5 * 3,
            start_time
        )
        opponent = OpponentCar(
            opponent_data["name"],
            opponent_data["color"]["0"],
            opponent_data["weight"],
            opponent_data["parts"],
            100, self.s_height // 5 * 3 + 100,
            start_time
        )
        timer = TimeToStart()

        while self.running:
            dt = self.clock.tick(60) / 1000.0
            keys = pygame.key.get_pressed()
            is_map_ended = bg.is_map_ended(map_distance)

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False

            self.screen.fill((20, 235, 35))
            bg.draw(self.screen)

            if timer.time > 0:
                timer.update_time()
                timer.draw(self.screen, self.s_width, self.s_height)
                player.update(None, None)
            else:
                player.update(keys, dt)
                opponent.update(dt)

                if not is_map_ended:
                    scroll_speed = player.speed * dt
                    bg.tick(scroll_speed)
                    distance_covered += scroll_speed
                    player.x_cord = 100
                else:
                    bg.x_cord = -map_distance

                player.draw(self.screen)
                opponent.draw(self.screen, distance_covered, is_map_ended, bg.x_cord)
                player_level = user_data['level']['current']
                player_exp = user_data['level']['exp']
                player_to_next_level = user_data['level']['required_to_next_level']
                player_money = user_data['money']
                if opponent.elapsed_time(map_distance):
                    reward = player_level
                    player_exp += int(reward * (2 + player_level / 10))
                    player_money += reward


                    if player_exp >= player_to_next_level:
                        player_level += 1
                        player_to_next_level += player_level * 100

                    # Update user_data dictionary with new values
                    user_data['level']['current'] = player_level
                    user_data['level']['exp'] = player_exp
                    user_data['level']['required_to_next_level'] = player_to_next_level
                    user_data['money'] = player_money

                    # Save updated user_data back to profile.json
                    with open('data/profile.json', 'w') as f:
                        json.dump(user_data, f, indent=4)

                    print(player_level)
                    return draw_end_screen(None, opponent.elapsed_time(map_distance), False, self.screen, self.s_width, reward, player_level)

                if player.elapsed_time(map_distance):
                    reward = int(250 * player_level / player.elapsed_time(map_distance))
                    player_exp += int(reward * (2 + player_level / 10))
                    player_money += reward

                    if player_exp >= player_to_next_level:
                        player_level += 1
                        player_to_next_level += player_level * 100

                    # Update user_data dictionary with new values
                    user_data['level']['current'] = player_level
                    user_data['level']['exp'] = player_exp
                    user_data['level']['required_to_next_level'] = player_to_next_level
                    user_data['money'] = player_money

                    # Save updated user_data back to profile.json
                    with open('data/profile.json', 'w') as f:
                        json.dump(user_data, f, indent=4)
                    return draw_end_screen(player.elapsed_time(map_distance), None, True, self.screen, self.s_width, reward, player_level)

            pygame.display.update()
