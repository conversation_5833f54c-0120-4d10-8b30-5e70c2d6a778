
import pygame
import json
from background import Background
from ui_components import Text<PERSON><PERSON>on, CarCard

class PartCard:
    def __init__(self, part_data, x, y, width, height, is_equipped=False):
        self.part_data = part_data
        self.rect = pygame.Rect(x, y, width, height)
        self.is_equipped = is_equipped
        self.is_hovered = False
        self.font = pygame.font.SysFont("arial", 20)
        self.header_font = pygame.font.SysFont("arial", 24)
    
    def update(self, mouse_pos, mouse_click):
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        if self.is_hovered and mouse_click[0]:
            return True
        return False
    
    def draw(self, screen):
        # Draw card background
        if self.is_equipped:
            bg_color = (20, 80, 20) if self.is_hovered else (10, 60, 10)
            border_color = (0, 255, 0)
        else:
            bg_color = (60, 60, 60) if self.is_hovered else (40, 40, 40)
            border_color = (200, 200, 200)
        
        pygame.draw.rect(screen, bg_color, self.rect)
        pygame.draw.rect(screen, border_color, self.rect, 1)
        
        # Draw part info
        y_offset = self.rect.y + 10
        
        # Part name
        name_color = (150, 255, 150) if self.is_equipped else (255, 255, 255)
        name_text = self.header_font.render(self.part_data["name"], True, name_color)
        screen.blit(name_text, (self.rect.x + 10, y_offset))
        y_offset += 30
        
        # Show "EQUIPPED" label if equipped
        if self.is_equipped:
            equipped_text = self.font.render("ZAMONTOWANE", True, (0, 255, 0))
            screen.blit(equipped_text, (self.rect.x + 10, y_offset))
            y_offset += 25
        
        # Part stats
        stats = []
        # Show base horsepower for engine part
        if "horsepower" in self.part_data and self.part_data.get("type") == "engine":
            stats.append(f"Moc silnika: {self.part_data['horsepower']} KM")
        elif "horsepower" in self.part_data:
            stats.append(f"Moc: {self.part_data['horsepower']} KM")
        if "horsepower_boost_percentage" in self.part_data:
            stats.append(f"Boost: +{self.part_data['horsepower_boost_percentage']}%")
        if "weight" in self.part_data:
            stats.append(f"Waga: {self.part_data['weight']} kg")
        
        for stat in stats:
            stat_text = self.font.render(stat, True, (200, 200, 200))
            screen.blit(stat_text, (self.rect.x + 10, y_offset))
            y_offset += 20

class CategoryTab:
    def __init__(self, label, x, y, width, height):
        self.rect = pygame.Rect(x, y, width, height)
        self.label = label
        self.is_active = False
        self.is_hovered = False
        self.font = pygame.font.SysFont("arial", 20)
    
    def update(self, mouse_pos, mouse_click):
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        if self.is_hovered and mouse_click[0]:
            return True
        return False
    
    def draw(self, screen):
        if self.is_active:
            bg_color = (0, 255, 255)
            text_color = (0, 0, 0)
        elif self.is_hovered:
            bg_color = (80, 80, 80)
            text_color = (255, 255, 255)
        else:
            bg_color = (50, 50, 50)
            text_color = (200, 200, 200)
        
        pygame.draw.rect(screen, bg_color, self.rect)
        pygame.draw.rect(screen, (200, 200, 200), self.rect, 1)
        
        text = self.font.render(self.label, True, text_color)
        text_x = self.rect.x + (self.rect.width - text.get_width()) // 2
        text_y = self.rect.y + (self.rect.height - text.get_height()) // 2
        screen.blit(text, (text_x, text_y))

def save_car_parts(car_index, equipped_parts):
    """Save equipped parts to garage.json"""
    with open('data/garage.json', 'r') as f:
        cars_data = json.load(f)
    
    # Update the car's parts
    cars_data[car_index]['parts'] = equipped_parts
    
    with open('data/garage.json', 'w') as f:
        json.dump(cars_data, f, indent=4)

def draw_enhanced_garage_screen(s_width, s_height, screen):
    run = True
    bg = Background('background', s_width, s_height)
    
    # Load data
    with open('data/garage.json') as f:
        cars_data = json.load(f)
    with open('data/shop_data.json') as f:
        shop_data = json.load(f)
    parts_data = shop_data[0]
    with open('data/profile.json') as f:
        profile_data = json.load(f)
    
    # Create buttons
    back_button = TextButton('Powrót', 50, 50, font_size=36)
    save_button = TextButton('Zapisz zmiany', s_width - 200, 50, font_size=36)

    # Get selected car
    selected_car_index = profile_data["cars"].get("selected_car", -1)
    owned_cars = profile_data.get("inventory", {}).get("owned_cars", [])
    if not owned_cars or selected_car_index < 0 or selected_car_index >= len(owned_cars):
        # No cars owned or invalid index, handle gracefully
        selected_car_index = -1
    
    if selected_car_index == -1:
        # No car to display, create a placeholder car card with no data
        selected_car = None
        current_color_index = None
        car_card = None
    else:
        selected_car = cars_data[selected_car_index]
        # Get color for this specific car
        car_colors = profile_data["cars"].get("car_colors", {})
        current_color_index = car_colors.get(str(selected_car_index))

        # If no color set, use first available color as default
        if current_color_index is None and selected_car:
            available_colors = list(selected_car["color"].keys())
            if available_colors:
                current_color_index = available_colors[0]
                # Save this default color
                if "car_colors" not in profile_data["cars"]:
                    profile_data["cars"]["car_colors"] = {}
                profile_data["cars"]["car_colors"][str(selected_car_index)] = current_color_index
                with open('data/profile.json', 'w') as f:
                    json.dump(profile_data, f, indent=4)

        car_card = CarCard(selected_car, 50, 150, 300, 350, True)
        car_card.set_color(current_color_index)

    # Color change buttons (only if car is selected)
    color_buttons = []
    if selected_car is not None:
        available_colors = selected_car["color"]
        button_width = 80
        button_height = 30
        spacing = 10
        start_x = 50
        start_y = 520

        for i, (color_index, color_name) in enumerate(available_colors.items()):
            x = start_x + i * (button_width + spacing)
            color_button = TextButton(color_name.title(), x, start_y, font_size=20)
            color_buttons.append((color_index, color_button))

    # Part categories
    categories = ["engine", "turbo", "intercooler", "ecu"]
    category_names = {
        "engine": "Silnik",
        "turbo": "Turbo",
        "intercooler": "Intercooler",
        "ecu": "ECU"
    }
    
    # Create category tabs
    tab_width = 100
    tab_height = 30
    tabs_per_row = 5
    tab_start_x = 400
    tab_start_y = 150
    
    category_tabs = []
    for i, category in enumerate(categories):
        row = i // tabs_per_row
        col = i % tabs_per_row
        x = tab_start_x + col * (tab_width + 10)
        y = tab_start_y + row * (tab_height + 10)
        tab = CategoryTab(category_names[category], x, y, tab_width, tab_height)
        category_tabs.append((category, tab))
    
    # Set initial category
    current_category = "engine"
    category_tabs[0][1].is_active = True

    # Track equipped parts (copy from current car)
    if selected_car is not None:
        equipped_parts = selected_car['parts'].copy()
        
        # Fill missing or null parts with default parts from parts_data
        for category in categories:
            if category not in equipped_parts or equipped_parts[category] is None:
                default_parts_list = parts_data.get(category, [])
                if default_parts_list:
                    equipped_parts[category] = default_parts_list[0]
    else:
        # No selected car, no equipped parts
        equipped_parts = {} 
    
    # Create fonts
    header_font = pygame.font.SysFont("arial", 48)
    info_font = pygame.font.SysFont("arial", 24)
    
    header = header_font.render("Garaż - Tuning", True, (255, 255, 255))
    
    while run:
        mouse_pos = pygame.mouse.get_pos()
        mouse_click = pygame.mouse.get_pressed()
        
        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                return
        
        # Update buttons
        back_button.update(mouse_pos, mouse_click)
        save_button.update(mouse_pos, mouse_click)

        # Update color buttons
        for color_index, color_button in color_buttons:
            color_button.update(mouse_pos, mouse_click)
        
        # Check buttons
        if back_button.is_hovered and mouse_click[0]:
            return
        
        if save_button.is_hovered and mouse_click[0]:
            if selected_car_index != -1:
                save_car_parts(selected_car_index, equipped_parts)
            return

        # Check color button clicks
        for color_index, color_button in color_buttons:
            if color_button.is_hovered and mouse_click[0]:
                # Update the color in profile
                if "car_colors" not in profile_data["cars"]:
                    profile_data["cars"]["car_colors"] = {}
                profile_data["cars"]["car_colors"][str(selected_car_index)] = color_index

                # Save profile changes
                with open('data/profile.json', 'w') as f:
                    json.dump(profile_data, f, indent=4)

                # Update car card color
                current_color_index = color_index
                if car_card:
                    car_card.set_color(current_color_index)
        
        # Update category tabs
        for category, tab in category_tabs:
            if tab.update(mouse_pos, mouse_click):
                current_category = category
                # Reset all tabs
                for _, t in category_tabs:
                    t.is_active = False
                tab.is_active = True
        
        # Get owned parts for current category
        owned_parts = profile_data["inventory"]["owned_parts"].get(current_category, [])
        available_parts = []
        
        # Find parts data for owned parts
        parts_list = parts_data.get(current_category, [])
        for part_name in owned_parts:
            for part in parts_list:
                if part["name"] == part_name:
                    available_parts.append(part)
                    break
        
        # Ensure equipped part is included even if not owned
        equipped_part = equipped_parts.get(current_category) if equipped_parts else None
        if equipped_part and all(p["name"] != equipped_part["name"] for p in available_parts):
            available_parts.append(equipped_part)
        
        # Create part cards
        part_cards = []
        cards_per_row = 3
        card_width = 200
        card_height = 150
        card_spacing = 20
        parts_start_x = 400
        parts_start_y = 250
        
        for i, part in enumerate(available_parts):
            row = i // cards_per_row
            col = i % cards_per_row
            x = parts_start_x + col * (card_width + card_spacing)
            y = parts_start_y + row * (card_height + card_spacing)
            
            # Check if this part is currently equipped
            is_equipped = (equipped_part is not None and equipped_part.get("name") == part["name"])
            
            card = PartCard(part, x, y, card_width, card_height, is_equipped)
            
            # Handle part card clicks
            if card.update(mouse_pos, mouse_click):
                # If this part is already equipped, unmount it
                if equipped_parts.get(current_category) and equipped_parts[current_category].get("name") == part["name"]:
                    equipped_parts[current_category] = None
                else:
                    # Equip this part (only one per category)
                    equipped_parts[current_category] = part
                
            part_cards.append(card)
        
        # Draw everything
        bg.draw(screen)
        
        # Draw header
        screen.blit(header, (s_width // 2 - header.get_width() // 2, 50))
        
        # Draw car card
        if car_card:
            car_card.draw(screen)
        
        # Draw category tabs
        for _, tab in category_tabs:
            tab.draw(screen)
        
        # Draw current category label
        category_label = info_font.render(f"Kategoria: {category_names[current_category]}", True, (255, 255, 255))
        screen.blit(category_label, (parts_start_x, parts_start_y - 30))
        
        # Draw part cards
        for card in part_cards:
            card.draw(screen)
        
        # Draw buttons
        back_button.draw(screen)
        save_button.draw(screen)

        # Draw color buttons
        if color_buttons:
            color_label = info_font.render("Kolory:", True, (255, 255, 255))
            screen.blit(color_label, (50, 490))
            for color_index, color_button in color_buttons:
                # Highlight current color
                if color_index == current_color_index:
                    color_button.hover_color = (255, 255, 0)  # Yellow for selected
                else:
                    color_button.hover_color = (0, 255, 255)  # Cyan for hover
                color_button.draw(screen)
        
        # Show instructions
        instruction = info_font.render("Kliknij na część, aby ją zamontować", True, (200, 200, 200))
        screen.blit(instruction, (parts_start_x, s_height - 100))
        
        pygame.display.update()
