import pygame
import json
from background import Background
from shop import ShopCard, TabButton, ConfirmDialog, save_purchase
from shop_screen import draw_shop_screen as shop_screen_draw
from garage_screen import draw_enhanced_garage_screen as garage_screen_draw
from ui_components import TextButton

class TextInput:
    def __init__(self, x, y, width, height, font_size=36, max_length=20):
        self.rect = pygame.Rect(x, y, width, height)
        self.font = pygame.font.SysFont("arial", font_size)
        self.text = ""
        self.max_length = max_length
        self.active = False
        self.cursor_visible = True
        self.cursor_timer = 0
        self.border_color = (200, 200, 200)
        self.active_color = (0, 255, 255)
        self.bg_color = (50, 50, 50)
        self.text_color = (255, 255, 255)
        
    def handle_event(self, event):
        if event.type == pygame.MOUSEBUTTONDOWN:
            self.active = self.rect.collidepoint(event.pos)
        elif event.type == pygame.KEYDOWN and self.active:
            if event.key == pygame.K_BACKSPACE:
                self.text = self.text[:-1]
            elif event.key == pygame.K_RETURN:
                return True  # Enter pressed
            elif len(self.text) < self.max_length and event.unicode.isprintable():
                self.text += event.unicode
        return False
    
    def update(self, dt):
        if self.active:
            self.cursor_timer += dt
            if self.cursor_timer >= 500:  # Blink every 500ms
                self.cursor_visible = not self.cursor_visible
                self.cursor_timer = 0
    
    def draw(self, screen):
        # Draw background
        pygame.draw.rect(screen, self.bg_color, self.rect)
        # Draw border
        border_color = self.active_color if self.active else self.border_color
        pygame.draw.rect(screen, border_color, self.rect, 2)
        
        # Draw text
        text_surface = self.font.render(self.text, True, self.text_color)
        screen.blit(text_surface, (self.rect.x + 10, self.rect.y + (self.rect.height - text_surface.get_height()) // 2))
        
        # Draw cursor
        if self.active and self.cursor_visible:
            cursor_x = self.rect.x + 10 + text_surface.get_width()
            cursor_y = self.rect.y + 5
            pygame.draw.line(screen, self.text_color, (cursor_x, cursor_y), (cursor_x, cursor_y + self.rect.height - 10), 2)


class TimeToStart:
    def __init__(self):
        self.start_time = pygame.time.get_ticks()
        self.time = 3

    def update_time(self):
        elapsed = (pygame.time.get_ticks() - self.start_time) // 1000
        self.time = max(0, 3 - elapsed)

    def draw(self, screen, s_width, s_height):
        font = pygame.font.SysFont("arial", 144)
        countdown = font.render(str(self.time), True, (255, 255, 255))
        screen.blit(countdown, (s_width // 2 - countdown.get_width() // 2, s_height // 2 - 100))

class ProgressBar:
    def __init__(self, x, y, width, height, progress, max_value):
        self.rect = pygame.Rect(x, y, width, height)
        self.progress = progress
        self.max_value = max_value
        self.border_color = (200, 200, 200)
        self.fill_color = (0, 255, 255)
        
    def draw(self, screen):
        # Draw border
        pygame.draw.rect(screen, self.border_color, self.rect, 2)
        # Draw fill
        fill_width = int(self.rect.width * (self.progress / self.max_value))
        fill_rect = pygame.Rect(self.rect.x, self.rect.y, fill_width, self.rect.height)
        pygame.draw.rect(screen, self.fill_color, fill_rect)

class CarCard:
    def __init__(self, car_data, x, y, width, height, is_selected=False):
        self.car_data = car_data
        self.rect = pygame.Rect(x, y, width, height)
        self.is_selected = is_selected
        self.is_hovered = False
        self.selected_color_index = "0"  # Default color
        self.font = pygame.font.SysFont("arial", 24)
        self.header_font = pygame.font.SysFont("arial", 32)
        
        # Load car image
        self.car_image = None
        self.load_car_image()
        
    def load_car_image(self):
        try:
            color_name = self.car_data["color"][self.selected_color_index]
            image_path = f"assets/img/{self.car_data['name']}_{color_name}_car.png"
            self.car_image = pygame.image.load(image_path)
            # Scale image to fit in card
            self.car_image = pygame.transform.scale(self.car_image, (150, 100))
        except:
            # Create a placeholder rectangle if image not found
            self.car_image = pygame.Surface((150, 100))
            self.car_image.fill((100, 100, 100))
    
    def update(self, mouse_pos, mouse_click):
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        if self.is_hovered and mouse_click[0]:
            return True  # Card was clicked
        return False
    
    def set_color(self, color_index):
        self.selected_color_index = color_index
        self.load_car_image()
    
    def draw(self, screen):
        # Draw card background
        bg_color = (80, 80, 80) if self.is_selected else (50, 50, 50)
        if self.is_hovered:
            bg_color = (100, 100, 100)
        
        pygame.draw.rect(screen, bg_color, self.rect)
        
        # Draw border
        border_color = (0, 255, 255) if self.is_selected else (200, 200, 200)
        border_width = 3 if self.is_selected else 1
        pygame.draw.rect(screen, border_color, self.rect, border_width)
        
        # Draw car image
        if self.car_image:
            image_x = self.rect.x + (self.rect.width - self.car_image.get_width()) // 2
            image_y = self.rect.y + 10
            screen.blit(self.car_image, (image_x, image_y))
        
        # Draw car info
        y_offset = self.rect.y + 120
        
        # Car name
        name_text = self.header_font.render(self.car_data["name"].title(), True, (255, 255, 255))
        screen.blit(name_text, (self.rect.x + 10, y_offset))
        y_offset += 35
        
        # Car stats
        color_name = self.car_data["color"][self.selected_color_index]
        color_text = self.font.render(f"Kolor: {color_name.title()}", True, (200, 200, 200))
        screen.blit(color_text, (self.rect.x + 10, y_offset))
        y_offset += 25
        
        weight_text = self.font.render(f"Waga: {self.car_data['weight']} kg", True, (200, 200, 200))
        screen.blit(weight_text, (self.rect.x + 10, y_offset))
        y_offset += 25
        
        value_text = self.font.render(f"Wartość: {self.car_data['value']} $", True, (200, 200, 200))
        screen.blit(value_text, (self.rect.x + 10, y_offset))
        y_offset += 25
        
        hp_text = self.font.render(f"Moc: {self.car_data['parts']['engine']['horsepower']} KM", True, (200, 200, 200))
        screen.blit(hp_text, (self.rect.x + 10, y_offset))

def save_new_username(new_username):
    with open('data/profile.json', 'r') as f:
        profile_data = json.load(f)

    profile_data['username'] = new_username

    with open('data/profile.json', 'w') as f:
        json.dump(profile_data, f, indent=4)

    # Auto-save if current save slot is set
    from save_system import save_system
    if save_system.current_save_slot:
        save_system.save_game(save_system.current_save_slot)

def save_car_selection(car_index, color_index):
    with open('data/profile.json', 'r') as f:
        profile_data = json.load(f)

    profile_data['cars']['selected_car'] = car_index

    # Initialize car_colors if it doesn't exist
    if "car_colors" not in profile_data["cars"]:
        profile_data["cars"]["car_colors"] = {}

    # Set the color for this specific car
    if color_index is not None:
        profile_data["cars"]["car_colors"][str(car_index)] = color_index

    with open('data/profile.json', 'w') as f:
        json.dump(profile_data, f, indent=4)

    # Auto-save if current save slot is set
    from save_system import save_system
    if save_system.current_save_slot:
        save_system.save_game(save_system.current_save_slot)

def change_username_screen(s_width, s_height, screen):
    run = True
    bg = Background('background', s_width, s_height)
    clock = pygame.time.Clock()
    
    # Create text input and buttons
    text_input = TextInput(s_width // 2 - 200, s_height // 2 - 25, 400, 50)
    back_button = TextButton('Powrót', 50, 50, font_size=36)
    save_button = TextButton('Zapisz', s_width - 200, s_height // 2 - 25, font_size=36)
    
    # Create fonts for messages
    header_font = pygame.font.SysFont("arial", 48)
    info_font = pygame.font.SysFont("arial", 36)
    
    header = header_font.render("Zmień nazwę użytkownika", True, (255, 255, 255))
    instruction = info_font.render("Wpisz nową nazwę i naciśnij Enter lub Zapisz", True, (200, 200, 200))
    
    while run:
        dt = clock.tick(60) / 1000.0  # Delta time in seconds
        mouse_pos = pygame.mouse.get_pos()
        mouse_click = pygame.mouse.get_pressed()
        
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                return
            
            # Handle text input
            if text_input.handle_event(event) and text_input.text.strip():  # Enter pressed with valid text
                save_new_username(text_input.text.strip())
                return
            
        # Update
        text_input.update(dt)
        back_button.update(mouse_pos, mouse_click)
        save_button.update(mouse_pos, mouse_click)
        
        # Check button clicks
        if back_button.is_hovered and mouse_click[0]:
            return
        
        if save_button.is_hovered and mouse_click[0] and text_input.text.strip():
            save_new_username(text_input.text.strip())
            return
        
        # Draw
        bg.draw(screen)
        screen.blit(header, (s_width // 2 - header.get_width() // 2, 150))
        screen.blit(instruction, (s_width // 2 - instruction.get_width() // 2, s_height // 2 - 100))
        
        text_input.draw(screen)
        back_button.draw(screen)
        save_button.draw(screen)
        
        pygame.display.update()

def draw_garage_screen(s_width, s_height, screen):
    run = True
    bg = Background('background', s_width, s_height)
    
    # Load car and profile data
    with open('data/cars.json') as f:
        cars_data = json.load(f)
    with open('data/profile.json') as f:
        profile_data = json.load(f)
    
    # Handle empty owned_cars or invalid selected_car index
    owned_cars = profile_data.get("inventory", {}).get("owned_cars", [])
    selected_car_index = profile_data.get("cars", {}).get("selected_car", -1)
    if not owned_cars or selected_car_index < 0 or selected_car_index >= len(owned_cars):
        # No cars owned or invalid index, allow access but no selection
        selected_car_index = -1
        profile_data["cars"]["selected_car"] = selected_car_index
    
    # Create buttons
    back_button = TextButton('Powrót', 50, 50, font_size=36)
    save_button = TextButton('Zapisz wybór', s_width - 200, 50, font_size=36)
    
    # Create car cards
    car_cards = []
    cards_per_row = 3
    card_width = 250
    card_height = 300
    card_spacing = 50
    start_x = (s_width - (cards_per_row * card_width + (cards_per_row - 1) * card_spacing)) // 2
    start_y = 150
    
    for i, car in enumerate(cars_data):
        row = i // cards_per_row
        col = i % cards_per_row
        x = start_x + col * (card_width + card_spacing)
        y = start_y + row * (card_height + card_spacing)
        
        is_selected = (car["index"] == selected_car_index)
        card = CarCard(car, x, y, card_width, card_height, is_selected)
        
        # Set the current color if this is the selected car
        if is_selected:
            car_colors = profile_data["cars"].get("car_colors", {})
            selected_color_index = car_colors.get(str(selected_car_index))
            if selected_color_index:
                card.set_color(selected_color_index)
            else:
                # Use first available color as default
                available_colors = list(car["color"].keys())
                if available_colors:
                    card.set_color(available_colors[0])
        
        car_cards.append(card)
    
    # Color selection buttons for the selected car
    color_buttons = []
    if selected_car_index == -1:
        current_color_index = None
    else:
        car_colors = profile_data["cars"].get("car_colors", {})
        current_color_index = car_colors.get(str(selected_car_index))
        if current_color_index is None:
            # Use first available color as default
            for car in cars_data:
                if car["index"] == selected_car_index:
                    available_colors = list(car["color"].keys())
                    if available_colors:
                        current_color_index = available_colors[0]
                    break
    
    # Create fonts
    header_font = pygame.font.SysFont("arial", 48)
    info_font = pygame.font.SysFont("arial", 24)
    
    header = header_font.render("Garaż", True, (255, 255, 255))
    instruction = info_font.render("Kliknij na samochód, aby go wybrać. Użyj przycisków kolorów, aby zmienić kolor.", True, (200, 200, 200))
    
    while run:
        mouse_pos = pygame.mouse.get_pos()
        mouse_click = pygame.mouse.get_pressed()
        
        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                return
        
        # Update buttons
        back_button.update(mouse_pos, mouse_click)
        save_button.update(mouse_pos, mouse_click)
        
        # Check back button
        if back_button.is_hovered and mouse_click[0]:
            return
        
        # Check save button
        if save_button.is_hovered and mouse_click[0]:
            save_car_selection(selected_car_index, current_color_index)
            return
        
        # Update car cards
        for i, card in enumerate(car_cards):
            if card.update(mouse_pos, mouse_click):
                # Car was selected
                selected_car_index = card.car_data["index"]
                current_color_index = "0"  # Reset to first color
                
                # Update selection status
                for c in car_cards:
                    c.is_selected = (c.car_data["index"] == selected_car_index)
                    if c.is_selected:
                        c.set_color(current_color_index)
        
        # Create color buttons for selected car
        color_buttons = []
        selected_car_data = None
        for car in cars_data:
            if car["index"] == selected_car_index:
                selected_car_data = car
                break
        
        if selected_car_data:
            color_button_y = s_height - 150
            color_button_spacing = 120
            colors = list(selected_car_data["color"].keys())
            start_color_x = (s_width - (len(colors) * color_button_spacing)) // 2
            
            for i, color_key in enumerate(colors):
                color_name = selected_car_data["color"][color_key]
                x = start_color_x + i * color_button_spacing
                is_current = (color_key == current_color_index)
                
                color_button = TextButton(
                    color_name.title(), 
                    x, color_button_y, 
                    font_size=24
                )
                
                # Highlight current color
                if is_current:
                    color_button.default_color = (0, 255, 255)
                
                color_button.update(mouse_pos, mouse_click)
                
                if color_button.is_hovered and mouse_click[0]:
                    current_color_index = color_key
                    # Update the selected car's color
                    for card in car_cards:
                        if card.is_selected:
                            card.set_color(current_color_index)
                
                color_buttons.append(color_button)
        
        # Draw everything
        bg.draw(screen)
        
        # Draw header and instructions
        screen.blit(header, (s_width // 2 - header.get_width() // 2, 50))
        screen.blit(instruction, (s_width // 2 - instruction.get_width() // 2, 100))
        
        # Draw car cards
        for card in car_cards:
            card.draw(screen)
        
        # Draw buttons
        back_button.draw(screen)
        save_button.draw(screen)
        
        # Draw color selection
        if selected_car_data:
            color_header = info_font.render("Wybierz kolor:", True, (255, 255, 255))
            screen.blit(color_header, (s_width // 2 - color_header.get_width() // 2, s_height - 200))
            
            for color_button in color_buttons:
                color_button.draw(screen)
        
        pygame.display.update()

def draw_profile_screen(s_width, s_height, screen):
    run = True
    bg = Background('background', s_width, s_height)
    
    # Load profile and car data
    with open('data/profile.json') as f:
        profile_data = json.load(f)
    with open('data/garage.json') as f:
        cars_data = json.load(f)
    
    # Handle empty owned_cars or invalid selected_car index
    owned_cars = profile_data.get("inventory", {}).get("owned_cars", [])
    selected_car_index = profile_data.get("cars", {}).get("selected_car", -1)
    if not owned_cars or selected_car_index < 0 or selected_car_index >= len(owned_cars):
        # No cars owned or invalid index, disable garage access or set default
        selected_car_index = -1
        profile_data["cars"]["selected_car"] = selected_car_index
    
    # Defensive assignment for selected_car and car_color
    selected_car = None
    car_color = None
    if selected_car_index != -1 and selected_car_index < len(cars_data):
        selected_car = cars_data[selected_car_index]
        # Get color for this specific car
        car_colors = profile_data["cars"].get("car_colors", {})
        selected_color_index = car_colors.get(str(selected_car_index))

        if selected_color_index and selected_color_index in selected_car["color"]:
            car_color = selected_car["color"][selected_color_index]
        else:
            # Use first available color as fallback
            available_colors = list(selected_car["color"].keys())
            if available_colors:
                car_color = selected_car["color"][available_colors[0]]
    
    total_horsepower = 0
    total_weight = 0
    if selected_car is not None:
        # Calculate total horsepower: engine base +
        engine_part = selected_car.get('parts', {}).get('engine')
        base_horsepower = engine_part.get('horsepower', 0) if engine_part else 0
        total_boost_percentage = 0
        total_weight = selected_car.get('weight', 0)
        
        for part_key, part in selected_car.get('parts', {}).items():
            if part_key != 'engine' and part is not None:
                # Add horsepower boost percentage if present
                boost_percentage = part.get('horsepower_boost_percentage', 0)
                total_boost_percentage += boost_percentage
                # Add weight of the part
                part_weight = part.get('weight', 0)
                total_weight += part_weight
        
        # Calculate total horsepower applying boost percentage
        total_horsepower = int(base_horsepower * (1 + total_boost_percentage / 100))
    
    # Create common UI elements
    back_button = TextButton('Powrót', 50, 50, font_size=36)
    change_username = TextButton('Zmień nazwę', s_width - 250, 50, font_size=36, 
                               action=lambda: change_username_screen(s_width, s_height, screen))
    
    # Create fonts
    header_font = pygame.font.SysFont("arial", 48)
    info_font = pygame.font.SysFont("arial", 36)
    
    # Create progress bar for exp
    exp_progress = ProgressBar(
        s_width // 4,
        s_height // 2 - 50,
        s_width // 2,
        30,
        profile_data["level"]["exp"],
        profile_data["level"]["required_to_next_level"]
    )

    # Initialize car-related variables
    total_horsepower = 0
    total_weight = 0
    if selected_car is not None:
        # Calculate car stats only if a car is selected
        engine_part = selected_car['parts'].get('engine')
        base_horsepower = engine_part.get('horsepower', 0) if engine_part else 0
        total_boost_percentage = 0
        total_weight = selected_car.get('weight', 0)
        
        for part_key, part in selected_car['parts'].items():
            if part_key != 'engine' and part is not None:
                boost_percentage = part.get('horsepower_boost_percentage', 0)
                total_boost_percentage += boost_percentage
                total_weight += part.get('weight', 0)
        
        total_horsepower = int(base_horsepower * (1 + total_boost_percentage / 100))
    
    while run:
        mouse_pos = pygame.mouse.get_pos()
        mouse_click = pygame.mouse.get_pressed()
        
        bg.draw(screen)
        
        # Update and draw buttons
        back_button.update(mouse_pos, mouse_click)
        change_username.update(mouse_pos, mouse_click)
        
        # Check if back button was clicked
        if back_button.is_hovered and mouse_click[0]:
            return
        
        # Draw player info section
        username = header_font.render(f"Gracz: {profile_data['username']}", True, (255, 255, 255))
        level = info_font.render(f"Poziom gracza: {profile_data['level']['current']}", True, (200, 200, 200))
        race_level = profile_data.get('race_level', profile_data['level']['current'])
        race_level_text = info_font.render(f"Poziom wyścigów: {race_level}", True, (200, 200, 200))
        money = info_font.render(f"Pieniądze: {profile_data['money']} $", True, (200, 200, 200))
        exp_text = info_font.render(
            f"EXP: {profile_data['level']['exp']}/{profile_data['level']['required_to_next_level']}",
            True, (200, 200, 200)
        )
        
        # Draw car info section
        car_header = header_font.render("Informacje o pojeździe:", True, (255, 255, 255))
        
        # Fix the error by checking if selected_car is None
        if selected_car is not None:
            car_name_text = selected_car.get('name', 'Unknown')
            car_name = info_font.render(f"Model: {car_name_text.title()}", True, (200, 200, 200))
            car_color_text = info_font.render(f"Kolor: {car_color.title() if car_color else 'Unknown'}", True, (200, 200, 200))
            car_weight = info_font.render(f"Waga: {total_weight} kg", True, (200, 200, 200))
            car_value = info_font.render(f"Wartość: {selected_car.get('value', 0)} $", True, (200, 200, 200))
            car_hp = info_font.render(f"Moc silnika: {total_horsepower} KM", True, (200, 200, 200))
        else:
            # Create placeholder text when no car is selected
            car_name = info_font.render("Model: Brak wybranego pojazdu", True, (200, 200, 200))
            car_color_text = info_font.render("Kolor: -", True, (200, 200, 200))
            car_weight = info_font.render("Waga: - kg", True, (200, 200, 200))
            car_value = info_font.render("Wartość: - $", True, (200, 200, 200))
            car_hp = info_font.render("Moc silnika: - KM", True, (200, 200, 200))
        
        # Draw all elements
        # Player info
        screen.blit(username, (s_width // 4, 150))
        screen.blit(level, (s_width // 4, 200))
        screen.blit(race_level_text, (s_width // 4, 240))
        screen.blit(money, (s_width // 4, 280))
        screen.blit(exp_text, (s_width // 4, s_height // 2 - 100))
        exp_progress.draw(screen)
        
        # Car info
        screen.blit(car_header, (s_width // 4, s_height // 2 + 50))
        screen.blit(car_name, (s_width // 4, s_height // 2 + 120))
        screen.blit(car_color_text, (s_width // 4, s_height // 2 + 170))
        screen.blit(car_weight, (s_width // 4, s_height // 2 + 220))
        screen.blit(car_value, (s_width // 4, s_height // 2 + 270))
        screen.blit(car_hp, (s_width // 4, s_height // 2 + 320))
        
        # Draw buttons
        back_button.draw(screen)
        change_username.draw(screen)
        
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                return
            
        pygame.display.update()

def draw_shop_screen(s_width, s_height, screen):
    run = True
    bg = Background('background', s_width, s_height)
    clock = pygame.time.Clock()
    
    # Load data
    with open('data/cars.json') as f:
        cars_data = json.load(f)
    with open('data/car_parts.json') as f:
        parts_data = json.load(f)
    with open('data/profile.json') as f:
        profile_data = json.load(f)
        
    # Scroll variables
    scroll_y = 0
    scroll_speed = 50
    
    # Create buttons
    back_button = TextButton('Powrót', 50, 50, font_size=36)
    
    # Create tabs
    tab_width = 150
    tab_height = 50
    tab_y = 120
    cars_tab = TabButton('Samochody', s_width // 2 - tab_width - 10, tab_y, tab_width, tab_height)
    parts_tab = TabButton('Części', s_width // 2 + 10, tab_y, tab_width, tab_height)
    
    # Set initial tab
    current_tab = "cars"
    cars_tab.is_active = True
    
    # Create fonts
    header_font = pygame.font.SysFont("arial", 48)
    info_font = pygame.font.SysFont("arial", 24)
    money_font = pygame.font.SysFont("arial", 36)
    
    header = header_font.render("Sklep", True, (255, 255, 255))
    
    # Dialog state
    confirm_dialog = None
    selected_item = None
    selected_item_type = None
    
    while run:
        mouse_pos = pygame.mouse.get_pos()
        mouse_click = pygame.mouse.get_pressed()
        
        
        # Update buttons
        back_button.update(mouse_pos, mouse_click)
        
        # Check back button
        if back_button.is_hovered and mouse_click[0] and not confirm_dialog:
            return
        
        # Handle dialog
        if confirm_dialog:
            dialog_result = confirm_dialog.update(mouse_pos, mouse_click)
            if dialog_result == "confirm":
                # Check if player has enough money
                if profile_data['money'] >= selected_item['value']:
                    save_purchase(selected_item, selected_item_type)
                    profile_data['money'] -= selected_item['value']
                confirm_dialog = None
                selected_item = None
                selected_item_type = None
            elif dialog_result == "cancel":
                confirm_dialog = None
                selected_item = None
                selected_item_type = None
        else:
            # Update tabs
            if cars_tab.update(mouse_pos, mouse_click):
                current_tab = "cars"
                cars_tab.is_active = True
                parts_tab.is_active = False
            
            if parts_tab.update(mouse_pos, mouse_click):
                current_tab = "parts"
                cars_tab.is_active = False
                parts_tab.is_active = True
        
        # Create shop cards based on current tab
        shop_cards = []
        if current_tab == "cars":
            items = cars_data
            item_type = "car"
        else:
            # Flatten parts data
            items = []
            for category, parts_list in parts_data.items():
                for part in parts_list:
                    part['category'] = category
                    items.append(part)
            item_type = "part"
        
        # Create cards layout
        cards_per_row = 4
        card_width = 250
        card_height = 300 if current_tab == "cars" else 200
        card_spacing = 30
        start_x = (s_width - (cards_per_row * card_width + (cards_per_row - 1) * card_spacing)) // 2
        start_y = 200 - scroll_y  # Apply scroll offset
        
        for i, item in enumerate(items):
            row = i // cards_per_row
            col = i % cards_per_row
            x = start_x + col * (card_width + card_spacing)
            y = start_y + row * (card_height + card_spacing)
            
            # Check if item is owned
            is_owned = False
            if item_type == "car":
                is_owned = item["index"] in profile_data["inventory"]["owned_cars"]
            else:
                is_owned = item["name"] in profile_data["inventory"]["owned_parts"][item["category"]]

            card = ShopCard(item, x, y, card_width, card_height, item_type, is_owned)
            
            # Check if card was clicked
            if not confirm_dialog and card.update(mouse_pos, mouse_click):
                # Only allow purchase if not owned and can afford
                if not is_owned and profile_data['money'] >= item['value']:
                    selected_item = item
                    selected_item_type = item_type
                    confirm_dialog = ConfirmDialog(item['name'], item['value'], s_width, s_height)
            
            shop_cards.append(card)
        
        # Handle scrolling with mouse wheel
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                if confirm_dialog:
                    confirm_dialog = None
                else:
                    return
            if event.type == pygame.MOUSEWHEEL:
                scroll_y -= event.y * 50  # Scroll up/down
                max_scroll = max(0, ((len(items) - 1) // cards_per_row + 1) * (card_height + card_spacing) - (s_height - 200))
                scroll_y = max(0, min(scroll_y, max_scroll))
        
        # Handle keyboard scrolling
        keys = pygame.key.get_pressed()
        if keys[pygame.K_UP] and scroll_y > 0:
            scroll_y = max(0, scroll_y - 5)
        if keys[pygame.K_DOWN]:
            max_scroll = max(0, ((len(items) - 1) // cards_per_row + 1) * (card_height + card_spacing) - (s_height - 200))
            scroll_y = min(max_scroll, scroll_y + 5)
            
        # Draw everything
        bg.draw(screen)
        
        # Draw fixed UI elements first
        screen.blit(header, (s_width // 2 - header.get_width() // 2, 50))
        money_text = money_font.render(f"Pieniądze: {profile_data['money']} $", True, (255, 255, 0))
        screen.blit(money_text, (s_width - money_text.get_width() - 50, 50))
        cars_tab.draw(screen)
        parts_tab.draw(screen)
        
        # Draw shop cards with clipping
        for card in shop_cards:
            # Only draw cards that are visible
            if card.rect.y + card.rect.height > 200 and card.rect.y < s_height:
                card.draw(screen)
        
        # Draw back button
        back_button.draw(screen)
        
        # Draw dialog if active
        if confirm_dialog:
            # Draw overlay
            overlay = pygame.Surface((s_width, s_height))
            overlay.set_alpha(128)
            overlay.fill((0, 0, 0))
            screen.blit(overlay, (0, 0))
            
            confirm_dialog.draw(screen)
        
        pygame.display.update()

def draw_end_screen(final_time, opponent_time, player_won, screen, s_width, reward, player_level):
    font_big = pygame.font.SysFont("arial", 72)
    font_small = pygame.font.SysFont("arial", 36)
    result = 'Wygrałeś!' if player_won else 'Przegrałeś!'
    header = font_big.render("Meta!", True, (255, 255, 255))
    result_text = font_big.render(result, True, (255, 255, 255))
    if opponent_time == None:
        time_text = font_small.render(f"Twój czas: {final_time:.2f}", True, (255, 255, 255))
        opp_text = None
    else:
        opp_text = font_small.render(f"Czas przeciwnika: {opponent_time:.2f}", True, (255, 255, 255))
        time_text = None
    reward_text = font_small.render(f"Nagroda: {reward} $$$ EXP: {int(reward * (2 + player_level / 10))}", True, (200, 200, 200))
    instruction = font_small.render("ESC, by wrócić do menu", True, (200, 200, 200))

    run = True
    while run:
        screen.fill((0, 0, 0))
        screen.blit(header, (s_width // 2 - header.get_width() // 2, 100))
        screen.blit(result_text, (s_width // 2 - result_text.get_width() // 2, 200))
        if time_text:
            screen.blit(time_text, (s_width // 2 - time_text.get_width() // 2, 300))
        else:
            screen.blit(opp_text, (s_width // 2 - opp_text.get_width() // 2, 300))
        screen.blit(reward_text, (s_width // 2 - reward_text.get_width() // 2, 350))
        screen.blit(instruction, (s_width // 2 - instruction.get_width() // 2, 500))

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                run = False
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                return
        
        pygame.display.update()
